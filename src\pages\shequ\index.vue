<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '社区',
    //开启下拉刷新
    enablePullDownRefresh: true,
    //页面上拉触底事件触发时距页面底部距离，单位只支持px
    onReachBottomDistance: 100,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="wrapper">
    <CustomNavbar title="社  区" />
    <KeFu />
    <view class="content">
      <CMSSearch
        placeholder="请输入您要搜索的关键字"
        @cityChange="handleCityChange"
        @search="handleSearch"
      />
      <view class="activity-list mt-6">
        <block v-for="item in self.activityList" :key="item.id">
          <view class="activity-item" @click="toActivityDetail(item.id)">
            <view class="info-wrapper">
              <view class="title">{{ item.title }}</view>
              <view class="address-info">
                <text class="iconfont icon"></text>
                <!-- <view class="text ellipsis">{{ item.address }}</view> -->
              </view>
              <view class="time-info">
                <text class="iconfont icon-shijian icon"></text>
                <text class="text">{{ item.add_time_date }}</text>
              </view>
            </view>
            <view class="image-wrapper">
              <image class="activity-image" :src="item.image_input[0]" mode="aspectFill"></image>
            </view>
          </view>
        </block>
        <view
          class="load-more"
          v-if="!pageData.loadend && activityList.length > 0"
          @click="loadActiveList"
        >
          <text v-if="!pageData.loading">点击加载更多</text>
          <text v-else>加载中...</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { activityList, myCancelJoin, myDelJoin, myActivityList, pay } from '@/service/user/index'
import { useToast, useMessage } from 'wot-design-uni'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import CMSSearch from '@/components/CMSSearch/index.vue'
import { useRequestPaging } from '@/hooks/useRequestPaging'
import KeFu from '@/components/KeFu/index.vue'
import CustomNavbar from '@/components/CustomNavbar/index.vue'

//
const { isComplete } = storeToRefs(useUserStore())
const { onShareAppMessage, onShareTimeline } = useShare()
const message = useMessage()
const toast = useToast()

onLoad(() => {
  // if (!isComplete.value) {
  //   message
  //     .confirm({
  //       msg: '您的资料不完整，暂时无法享受全部功能',
  //       title: '系统提醒',
  //       confirmButtonText: '立即完善',
  //       cancelButtonText: '稍后再说',
  //     })
  //     .then(() => {
  //       uni.navigateTo({ url: '/pages/login/information' })
  //     })
  //     .catch(() => {
  //       console.log('点击了取消按钮')
  //     })
  // }
})
// 监听滚动高度
onPageScroll((res) => {
  uni.$emit('onPageScroll', res.scrollTop)
})

const self = reactive<{
  activityList: any[] // 未过期
  expiredActivity: any[] // 过期
  userList: any[]
  status: string
}>({
  activityList: [],
  expiredActivity: [],
  userList: [],
  status: '',
})
// page: 1, // 列表页码
// limit: 10, // 每页条数
// loading: false, // 是否正在加载更多
// finished: false, // 是否加载完成
// totalNum: 0 // 总数
const pageData = reactive({
  page: 1,
  limit: 10,
  loading: false,
  loadend: false,
  state: 'loading' as 'loading' | 'finished',
  cityId: '',
  title: '',
  totalNum: 0,
})
const reset = () => {
  pageData.page = 1
  pageData.loadend = false
  pageData.loading = false
  self.activityList = []
  self.expiredActivity = []
  loadActiveList()
}

const handleCityChange = (e) => {
  pageData.cityId = e
  reset()
}
const handleSearch = (e) => {
  pageData.title = e
  reset()
}

const loadActiveList = async () => {
  if (pageData.loadend) return
  pageData.loading = true

  const [res, err] = await activityList({
    classify: 2,
    type: 0,
    page: pageData.page,
    limit: pageData.limit,
    title: pageData.title,
    city_id: pageData.cityId,
  })

  if (res) {
    console.log('res :>> ', res)
    const list = res.data.list
    const loadend = list.length < pageData.limit
    pageData.loadend = loadend
    pageData.page++
    pageData.state = loadend ? 'finished' : 'loading'
    self.activityList = [...self.activityList, ...list]
  }
  pageData.loading = false
}
loadActiveList()

// 去活动详情
const toActivityDetail = (id) => {
  uni.navigateTo({
    url: '/pages/shequ/detail?id=' + id,
  })
}
// 监听下拉刷新
onPullDownRefresh(() => {
  reset()
  setTimeout(() => {
    // 关闭下拉刷新
    uni.stopPullDownRefresh()
  }, 1000)
})
//
onReachBottom(() => {
  loadActiveList()
})
</script>

<style lang="scss" scoped>
.wrapper {
  position: relative;
  min-height: 100vh;
  padding-top: 20rpx;

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 442rpx;
    content: '';
    // background: url('http://cdn.sanzeyisheng.cdhuyun.cn/store/image/bg1.png') no-repeat;
    background: linear-gradient(270deg, #f9225e, #ffb5ca);
    background-size: 100% 100%;
  }
}

.content {
  position: relative;
  z-index: 10;
  padding: 0 28rpx 28rpx;
}

.tab {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 40rpx;

  &__item {
    flex: 1;

    &:last-child {
      margin-left: 62rpx;
    }

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 71rpx;
      font-size: 28rpx;
      font-weight: normal;
      color: #f9225e;
      border: 1rpx solid #fb225e;
      border-radius: 36rpx;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
    }

    .active {
      color: #ffffff;
      background: linear-gradient(24deg, #ff94b2, #f9225e);
      border: none;
    }
  }
}

.activity-list {
  .activity-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18rpx;
    margin-bottom: 20rpx;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
  }
}

.image-wrapper {
  position: relative;

  .activity-image {
    width: 180rpx;
    height: 180rpx;
    border-radius: 20rpx;
  }
}

.info-wrapper {
  flex: 1;
  margin-left: 18rpx;

  .title {
    font-size: 26rpx;
    font-weight: 500;
    color: #303030;

    .status-box {
      font-size: 20rpx;
      color: #303030;
    }

    .status-1 {
      color: #f9225e;
    }

    .status-2 {
      color: #e84510;
    }

    .status-3 {
      color: #767676;
    }
  }

  .time-info,
  .address-info {
    display: flex;
    align-items: center;
    margin-top: 20rpx;

    .icon {
      margin-right: 10rpx;
      color: #ff225e;
    }

    .text {
      font-size: 26rpx;
      color: #303030;
    }
  }

  .address-info {
    margin-top: 21rpx;

    .text {
      max-width: 200rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.operation {
  margin-left: 46rpx;

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 104rpx;
    height: 57rpx;
    padding: 0 10rpx;
    font-size: 20rpx;
    color: #ffffff;
    background: linear-gradient(-31deg, #ff94b2, #f9225e);
    border-radius: 29rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
  }

  .no {
    color: #7f7f7f;
    background: linear-gradient(-31deg, #f5efef, #f5efef, #f4efef);
  }

  .btn-2 {
    margin-top: 18rpx;
    color: #303030;
    background: linear-gradient(-31deg, #f5efef, #f5efef, #f4efef) !important;
  }

  .number {
    margin-top: 80rpx;
    font-size: 26rpx;
    font-weight: 500;
    color: #f9225e;
  }

  .price {
    margin-top: 18rpx;
    font-size: 28rpx;
    color: #9a6bff;
  }

  .over {
    color: #7f7f7f !important;
  }
}

.history-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 40rpx 0;
  font-size: 28rpx;
  color: #7f7f7f;

  .line {
    width: 263rpx;
    height: 1rpx;
    background: #a0a0a3;
  }
}

// 历史活动项的特殊样式
.activity-item.history {
  .title,
  .icon,
  .text,
  .number {
    color: #ccc;
  }
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.activity-status-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  margin-top: 64rpx;

  .activity-status-item {
    padding: 8rpx 30rpx;
    font-size: 26rpx;
    color: #303030;
    border-radius: 36rpx;
    transition: all 0.3s;
  }

  .active {
    color: #ffffff;
    background: linear-gradient(24deg, #ff94b2, #f9225e);
    border-image: linear-gradient(0deg, #ff94b2, #f9225e) 1 1;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
  }
}

.load-more {
  padding: 20rpx;
  text-align: center;
  cursor: pointer;
  background-color: #fff;
}
</style>
